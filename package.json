{"name": "geocoding-tool", "version": "0.5.0", "description": "经纬度逆向工具 - 将Excel文件中的经纬度坐标转换为省市区信息", "main": "app.js", "scripts": {"start": "python server.py", "dev": "python server.py", "build": "node -e \"const fs=require('fs');const path=require('path');if(!fs.existsSync('dist')){fs.mkdirSync('dist')};fs.copyFileSync('index.html',path.join('dist','index.html'));fs.copyFileSync('app.js',path.join('dist','app.js'));if(fs.existsSync('README.md')){fs.copyFileSync('README.md',path.join('dist','README.md'))};console.log('打包完成，文件已复制到dist目录');\"", "deploy:gh-pages": "npm run build && gh-pages -d dist"}, "keywords": ["geocoding", "excel", "map", "coordinates"], "author": "", "license": "MIT", "devDependencies": {"gh-pages": "^6.0.0"}}