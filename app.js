/**
 * 经纬度逆向工具 - 主要JavaScript文件
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0: 代码重构优化，提取工具函数，优化性能，简化复杂逻辑
 * - v0.5.0: 原始版本
 */

// 应用状态管理
const AppState = {
    workbook: null,
    worksheet: null,
    headers: [],
    data: [],
    resultData: [],
    longitudeColIndex: -1,
    latitudeColIndex: -1,
    processStats: { success: 0, failure: 0, skipped: 0 }
};

// 常量配置
const CONFIG = {
    API_KEY_STORAGE_KEY: 'geoApiKey',
    GEO_URL: "https://restapi.amap.com/v3/geocode/regeo",
    MAX_DISPLAY_ROWS: 5,
    BATCH_SIZE: 2,
    BATCH_DELAY: 1000,
    REQUEST_TIMEOUT: 10000
};

// 工具函数模块
const Utils = {
    // 本地存储操作
    storage: {
        isAvailable() {
            try {
                const testKey = '__test_storage__';
                localStorage.setItem(testKey, testKey);
                const result = localStorage.getItem(testKey);
                localStorage.removeItem(testKey);
                return result === testKey;
            } catch (error) {
                return false;
            }
        },

        get(key) {
            try {
                return localStorage.getItem(key);
            } catch (error) {
                return null;
            }
        },

        set(key, value) {
            try {
                localStorage.setItem(key, value);
                return true;
            } catch (error) {
                return false;
            }
        },

        remove(key) {
            try {
                localStorage.removeItem(key);
                return true;
            } catch (error) {
                return false;
            }
        }
    },

    // DOM操作工具
    dom: {
        createElement(tag, className, textContent) {
            const element = document.createElement(tag);
            if (className) element.className = className;
            if (textContent) element.textContent = textContent;
            return element;
        },

        clearElement(element) {
            while (element.firstChild) {
                element.removeChild(element.firstChild);
            }
        },

        createTableRow(data, cellClassName) {
            const tr = document.createElement('tr');
            data.forEach(cellData => {
                const td = this.createElement('td', cellClassName, cellData || '');
                tr.appendChild(td);
            });
            return tr;
        }
    },

    // 格式化工具
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 坐标验证
    isValidCoordinate(longitude, latitude) {
        return !(longitude === undefined || latitude === undefined ||
                 longitude === '' || latitude === '' ||
                 isNaN(parseFloat(longitude)) || isNaN(parseFloat(latitude)));
    }
};

// 优化后的请求队列管理器
const RequestQueue = {
    maxConcurrent: 3,
    requestInterval: 300,
    queue: [],
    running: 0,
    lastRequestTime: 0,

    enqueue(requestFn) {
        return new Promise((resolve, reject) => {
            this.queue.push({ requestFn, resolve, reject });
            this.processQueue();
        });
    },

    async processQueue() {
        if (this.running >= this.maxConcurrent || this.queue.length === 0) return;

        const { requestFn, resolve, reject } = this.queue.shift();
        this.running++;

        try {
            const now = Date.now();
            const timeToWait = Math.max(0, this.requestInterval - (now - this.lastRequestTime));
            if (timeToWait > 0) await new Promise(r => setTimeout(r, timeToWait));

            this.lastRequestTime = Date.now();
            const result = await requestFn();
            resolve(result);
        } catch (error) {
            reject(error);
        } finally {
            this.running--;
            this.processQueue();
        }
    },

    reset() {
        this.queue = [];
        this.running = 0;
        this.lastRequestTime = 0;
    }
};

// DOM元素缓存 - 优化DOM查询性能
const Elements = {
    // 文件相关
    fileInput: null,
    fileInfo: null,
    fileName: null,
    fileSize: null,

    // API Key相关
    apiKeyInput: null,
    toggleApiKeyBtn: null,
    eyeIcon: null,
    eyeOffIcon: null,
    saveApiKeyCheckbox: null,

    // 按钮
    nextBtn: null,
    backBtn: null,
    processBtn: null,
    newFileBtn: null,
    downloadBtn: null,

    // 表单控件
    testModeCheckbox: null,
    longitudeColSelect: null,
    latitudeColSelect: null,

    // 进度相关
    progressBar: null,
    progressText: null,
    processingStatus: null,
    resultSummary: null,

    // 步骤相关
    step1: null,
    step2: null,
    step3: null,
    processing: null,
    step1Indicator: null,
    step2Indicator: null,
    step3Indicator: null,

    // 表格相关
    tableHeader: null,
    tableBody: null,
    resultTableHeader: null,
    resultTableBody: null,

    // 初始化所有DOM元素引用
    init() {
        // 文件相关
        this.fileInput = document.getElementById('fileInput');
        this.fileInfo = document.getElementById('fileInfo');
        this.fileName = document.getElementById('fileName');
        this.fileSize = document.getElementById('fileSize');

        // API Key相关
        this.apiKeyInput = document.getElementById('apiKey');
        this.toggleApiKeyBtn = document.getElementById('toggleApiKey');
        this.eyeIcon = document.getElementById('eyeIcon');
        this.eyeOffIcon = document.getElementById('eyeOffIcon');
        this.saveApiKeyCheckbox = document.getElementById('saveApiKey');

        // 按钮
        this.nextBtn = document.getElementById('nextBtn');
        this.backBtn = document.getElementById('backBtn');
        this.processBtn = document.getElementById('processBtn');
        this.newFileBtn = document.getElementById('newFileBtn');
        this.downloadBtn = document.getElementById('downloadBtn');

        // 表单控件
        this.testModeCheckbox = document.getElementById('testMode');
        this.longitudeColSelect = document.getElementById('longitudeCol');
        this.latitudeColSelect = document.getElementById('latitudeCol');

        // 进度相关
        this.progressBar = document.getElementById('progressBar');
        this.progressText = document.getElementById('progressText');
        this.processingStatus = document.getElementById('processingStatus');
        this.resultSummary = document.getElementById('resultSummary');

        // 步骤相关
        this.step1 = document.getElementById('step1');
        this.step2 = document.getElementById('step2');
        this.step3 = document.getElementById('step3');
        this.processing = document.getElementById('processing');
        this.step1Indicator = document.getElementById('step1-indicator');
        this.step2Indicator = document.getElementById('step2-indicator');
        this.step3Indicator = document.getElementById('step3-indicator');

        // 表格相关
        this.tableHeader = document.getElementById('tableHeader');
        this.tableBody = document.getElementById('tableBody');
        this.resultTableHeader = document.getElementById('resultTableHeader');
        this.resultTableBody = document.getElementById('resultTableBody');
    }
};

// 应用初始化
const App = {
    init() {
        // 初始化DOM元素引用
        Elements.init();

        // 检查localStorage是否可用
        this.checkLocalStorageAvailability();

        // 绑定事件监听器
        this.bindEventListeners();

        // 设置拖拽文件功能
        this.setupDragAndDrop();

        // 从本地存储加载API Key
        this.loadApiKeyFromStorage();
    },

    bindEventListeners() {
        // 文件选择事件
        Elements.fileInput.addEventListener('change', FileHandler.handleFileSelect);

        // 按钮事件
        Elements.nextBtn.addEventListener('click', StepManager.goToStep2);
        Elements.backBtn.addEventListener('click', StepManager.goToStep1);
        Elements.processBtn.addEventListener('click', DataProcessor.startProcessing);
        Elements.newFileBtn.addEventListener('click', this.resetApp);
        Elements.downloadBtn.addEventListener('click', FileHandler.downloadResult);

        // API Key相关事件
        Elements.apiKeyInput.addEventListener('input', ApiKeyManager.handleInput);
        Elements.saveApiKeyCheckbox.addEventListener('change', ApiKeyManager.handleSaveOptionChange);
        Elements.toggleApiKeyBtn.addEventListener('click', ApiKeyManager.toggleVisibility);

        // 列选择事件
        Elements.longitudeColSelect.addEventListener('change', function() {
            AppState.longitudeColIndex = parseInt(this.value);
        });

        Elements.latitudeColSelect.addEventListener('change', function() {
            AppState.latitudeColIndex = parseInt(this.value);
        });
    },

    checkLocalStorageAvailability() {
        const isAvailable = Utils.storage.isAvailable();

        if (!isAvailable) {
            Elements.saveApiKeyCheckbox.disabled = true;
            Elements.saveApiKeyCheckbox.checked = false;

            const saveApiKeyLabel = document.querySelector('label[for="saveApiKey"]');
            if (saveApiKeyLabel) {
                saveApiKeyLabel.textContent = '保存API Key功能不可用（浏览器不支持或已禁用存储）';
                saveApiKeyLabel.classList.add('text-red-500');
            }
        }

        return isAvailable;
    },

    loadApiKeyFromStorage() {
        const savedApiKey = Utils.storage.get(CONFIG.API_KEY_STORAGE_KEY);

        if (savedApiKey) {
            Elements.apiKeyInput.value = savedApiKey;
            Elements.saveApiKeyCheckbox.checked = true;
            this.validateForm();
        } else {
            Elements.saveApiKeyCheckbox.checked = false;
        }
    },

    validateForm() {
        const apiKey = Elements.apiKeyInput.value.trim();
        Elements.nextBtn.disabled = !Elements.fileInput.files[0] || !apiKey;
    },

    resetApp() {
        // 重置应用状态
        Object.assign(AppState, {
            workbook: null,
            worksheet: null,
            headers: [],
            data: [],
            resultData: [],
            longitudeColIndex: -1,
            latitudeColIndex: -1,
            processStats: { success: 0, failure: 0, skipped: 0 }
        });

        // 重置请求队列
        RequestQueue.reset();

        // 重置UI
        Elements.fileInput.value = '';
        Elements.fileInfo.classList.add('hidden');
        Elements.testModeCheckbox.checked = false;
        Elements.nextBtn.disabled = true;

        // 重置步骤状态
        StepManager.resetToStep1();
    },

    setupDragAndDrop() {
        const dropZone = document.querySelector('.border-dashed');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, this.preventDefaults, false);
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            dropZone.addEventListener(eventName, this.highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropZone.addEventListener(eventName, this.unhighlight, false);
        });

        dropZone.addEventListener('drop', this.handleDrop, false);
    },

    preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    },

    highlight() {
        const dropZone = document.querySelector('.border-dashed');
        dropZone.classList.add('border-blue-500');
        dropZone.classList.remove('border-gray-300');
    },

    unhighlight() {
        const dropZone = document.querySelector('.border-dashed');
        dropZone.classList.remove('border-blue-500');
        dropZone.classList.add('border-gray-300');
    },

    handleDrop(e) {
        const dt = e.dataTransfer;
        const files = dt.files;

        if (files.length > 0) {
            Elements.fileInput.files = files;
            FileHandler.handleFileSelect();
        }
    }
};

// 页面加载完成后初始化应用
document.addEventListener('DOMContentLoaded', () => {
    App.init();
});

// API Key管理模块
const ApiKeyManager = {
    handleInput() {
        App.validateForm();
        this.saveApiKey();
    },

    handleSaveOptionChange() {
        this.saveApiKey();
    },

    saveApiKey() {
        const apiKey = Elements.apiKeyInput.value.trim();

        if (apiKey && Elements.saveApiKeyCheckbox.checked) {
            if (Utils.storage.set(CONFIG.API_KEY_STORAGE_KEY, apiKey)) {
                this.showSaveStatus(true);
            } else {
                this.showSaveStatus(false, '存储失败');
            }
        } else {
            Utils.storage.remove(CONFIG.API_KEY_STORAGE_KEY);
            if (apiKey) {
                this.showSaveStatus(false);
            }
        }
    },

    showSaveStatus(isSuccess, errorMsg) {
        let statusEl = document.getElementById('apiKeySaveStatus');
        if (!statusEl) {
            statusEl = Utils.dom.createElement('div', 'text-sm mt-1 transition-opacity duration-300');
            statusEl.id = 'apiKeySaveStatus';

            const parentEl = Elements.saveApiKeyCheckbox.closest('div');
            if (parentEl) {
                parentEl.appendChild(statusEl);
            }
        }

        if (isSuccess) {
            statusEl.textContent = 'API Key已保存到本地';
            statusEl.className = 'text-sm mt-1 text-green-600 transition-opacity duration-300';
        } else {
            statusEl.textContent = errorMsg ? `保存失败: ${errorMsg}` : 'API Key未保存';
            statusEl.className = 'text-sm mt-1 text-red-500 transition-opacity duration-300';
        }

        statusEl.style.opacity = '1';
        setTimeout(() => {
            statusEl.style.opacity = '0';
        }, 3000);
    },

    toggleVisibility() {
        if (Elements.apiKeyInput.type === 'password') {
            Elements.apiKeyInput.type = 'text';
            Elements.eyeIcon.classList.add('hidden');
            Elements.eyeOffIcon.classList.remove('hidden');
        } else {
            Elements.apiKeyInput.type = 'password';
            Elements.eyeIcon.classList.remove('hidden');
            Elements.eyeOffIcon.classList.add('hidden');
        }
    }
};

// 文件处理模块
const FileHandler = {
    handleFileSelect() {
        const file = Elements.fileInput.files[0];
        if (!file) return;

        // 显示文件信息
        Elements.fileName.textContent = file.name;
        Elements.fileSize.textContent = Utils.formatFileSize(file.size);
        Elements.fileInfo.classList.remove('hidden');

        // 读取Excel文件
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const excelData = new Uint8Array(e.target.result);
                AppState.workbook = XLSX.read(excelData, { type: 'array' });

                // 获取第一个工作表
                const firstSheetName = AppState.workbook.SheetNames[0];
                AppState.worksheet = AppState.workbook.Sheets[firstSheetName];

                // 转换为JSON
                AppState.data = XLSX.utils.sheet_to_json(AppState.worksheet, { header: 1 });

                if (AppState.data.length > 0) {
                    AppState.headers = AppState.data[0];
                    App.validateForm();
                } else {
                    this.showError('Excel文件没有数据');
                }
            } catch (error) {
                this.showError('无法读取Excel文件: ' + error.message);
            }
        };
        reader.readAsArrayBuffer(file);
    },

    showError(message) {
        alert(message);
    },

    downloadResult() {
        try {
            const newWb = XLSX.utils.book_new();

            // 创建原始数据工作表
            const originalWs = XLSX.utils.json_to_sheet(
                AppState.resultData.slice(1).map(row => {
                    const obj = {};
                    AppState.resultData[0].forEach((header, index) => {
                        obj[header] = row[index];
                    });
                    return obj;
                })
            );

            XLSX.utils.book_append_sheet(newWb, originalWs, '原始数据');

            // 检查是否有详细数据
            if (window.detailData && window.detailData.length > 1) {
                const detailWs = XLSX.utils.aoa_to_sheet(window.detailData);
                XLSX.utils.book_append_sheet(newWb, detailWs, '详细地址信息');
            }

            // 生成文件名
            const originalFileName = Elements.fileName.textContent;
            const baseName = originalFileName.replace(/\.[^/.]+$/, '');
            const newFileName = `${baseName}_结果.xlsx`;

            // 下载文件
            XLSX.writeFile(newWb, newFileName);

            console.log(`成功导出文件 ${newFileName}，包含 ${AppState.processStats.success} 条成功记录`);
        } catch (error) {
            this.showError('下载结果失败: ' + error.message);
        }
    }
};

// 步骤管理模块
const StepManager = {
    goToStep2() {
        Elements.step1.classList.add('hidden');
        Elements.step2.classList.remove('hidden');
        this.updateStepIndicator(1, 'green');
        this.updateStepIndicator(2, 'blue');

        // 预览数据和设置列选择器
        DataPreview.previewData();
        DataPreview.populateColumnSelectors();
    },

    goToStep1() {
        Elements.step2.classList.add('hidden');
        Elements.step1.classList.remove('hidden');
        this.updateStepIndicator(2, 'gray');
        this.updateStepIndicator(1, 'blue');
    },

    goToStep3() {
        Elements.step2.classList.add('hidden');
        Elements.step3.classList.remove('hidden');
        this.updateStepIndicator(3, 'green');
    },

    showProcessing() {
        Elements.step2.classList.add('hidden');
        Elements.processing.classList.remove('hidden');
    },

    hideProcessing() {
        Elements.processing.classList.add('hidden');
    },

    resetToStep1() {
        Elements.step3.classList.add('hidden');
        Elements.step1.classList.remove('hidden');
        this.updateStepIndicator(3, 'gray');
        this.updateStepIndicator(2, 'gray');
        this.updateStepIndicator(1, 'blue');
    },

    updateStepIndicator(step, color) {
        const indicator = Elements[`step${step}Indicator`];
        const colors = {
            blue: ['bg-blue-500', 'bg-gray-300', 'bg-green-500'],
            green: ['bg-green-500', 'bg-blue-500', 'bg-gray-300'],
            gray: ['bg-gray-300', 'bg-blue-500', 'bg-green-500']
        };

        indicator.classList.remove(...colors.blue, ...colors.green, ...colors.gray);
        indicator.classList.add(`bg-${color}-500`);
    }
};

// 数据预览模块
const DataPreview = {
    previewData() {
        // 清空表格
        Utils.dom.clearElement(Elements.tableHeader);
        Utils.dom.clearElement(Elements.tableBody);

        // 添加表头
        AppState.headers.forEach(header => {
            const th = Utils.dom.createElement('th',
                'px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                header);
            Elements.tableHeader.appendChild(th);
        });

        // 添加数据行 (最多显示5行)
        const rowsToShow = Math.min(CONFIG.MAX_DISPLAY_ROWS, AppState.data.length - 1);
        for (let i = 1; i <= rowsToShow; i++) {
            const row = AppState.data[i];
            const tr = Utils.dom.createTableRow(
                row.map(cell => cell !== undefined ? cell : ''),
                'px-6 py-4 whitespace-nowrap text-sm text-gray-500'
            );
            Elements.tableBody.appendChild(tr);
        }
    },

    populateColumnSelectors() {
        // 清空选择器
        Utils.dom.clearElement(Elements.longitudeColSelect);
        Utils.dom.clearElement(Elements.latitudeColSelect);

        // 添加选项
        AppState.headers.forEach((header, index) => {
            const option1 = Utils.dom.createElement('option', '', header);
            option1.value = index;

            const option2 = Utils.dom.createElement('option', '', header);
            option2.value = index;

            Elements.longitudeColSelect.appendChild(option1);
            Elements.latitudeColSelect.appendChild(option2);

            // 自动选择经纬度列
            if (header.includes('经度')) {
                Elements.longitudeColSelect.value = index;
                AppState.longitudeColIndex = index;
            }

            if (header.includes('纬度')) {
                Elements.latitudeColSelect.value = index;
                AppState.latitudeColIndex = index;
            }
        });
    }
};

// 数据处理模块
const DataProcessor = {
    async startProcessing() {
        const apiKey = Elements.apiKeyInput.value.trim();
        const isTestMode = Elements.testModeCheckbox.checked;

        // 验证选择
        if (AppState.longitudeColIndex === -1 || AppState.latitudeColIndex === -1) {
            FileHandler.showError('请选择经度和纬度列');
            return;
        }

        // 重置请求队列和统计
        RequestQueue.reset();
        AppState.processStats = { success: 0, failure: 0, skipped: 0 };

        // 显示处理界面
        StepManager.showProcessing();

        // 准备处理数据
        AppState.resultData = JSON.parse(JSON.stringify(AppState.data));
        AppState.resultData[0].push('处理状态');

        // 创建详细信息数据
        const detailData = this.createDetailDataStructure();

        // 确定处理范围
        const { startRow, endRow, totalRows } = this.calculateProcessingRange(isTestMode);

        try {
            await this.processBatches(startRow, endRow, totalRows, apiKey, detailData);

            // 保存详细数据供下载使用
            window.detailData = detailData;

            this.updateProgress(100,
                `处理完成 (成功: ${AppState.processStats.success}, 失败: ${AppState.processStats.failure}, 跳过: ${AppState.processStats.skipped})`);
        } catch (error) {
            this.updateProgress(100,
                `处理中断 (成功: ${AppState.processStats.success}, 失败: ${AppState.processStats.failure}, 跳过: ${AppState.processStats.skipped})`);
        } finally {
            setTimeout(() => {
                StepManager.hideProcessing();
                ResultDisplay.showResults();
            }, 500);
        }
    },

    createDetailDataStructure() {
        const detailHeaders = [
            '原始行号', 'POI ID', 'POI名称', 'POI类型', 'POI电话', 'POI距离',
            '经度', '纬度', '格式化地址', '国家', '省份', '城市', '区县',
            '城市编码', '区域编码', '乡镇街道', '乡镇街道编码', '社区名称',
            '社区类型', '建筑物名称', '建筑物类型', '街道名称', '门牌号',
            '方位', '距离', '所属海域', '商圈信息'
        ];
        return [detailHeaders];
    },

    calculateProcessingRange(isTestMode) {
        const startRow = 1; // 跳过表头
        const endRow = isTestMode ? Math.min(6, AppState.resultData.length) : AppState.resultData.length;
        const totalRows = endRow - startRow;
        return { startRow, endRow, totalRows };
    },

    async processBatches(startRow, endRow, totalRows, apiKey, detailData) {
        for (let batchStart = startRow; batchStart < endRow; batchStart += CONFIG.BATCH_SIZE) {
            const batchEnd = Math.min(batchStart + CONFIG.BATCH_SIZE, endRow);
            const batchPromises = [];

            // 创建当前批次的所有请求
            for (let i = batchStart; i < batchEnd; i++) {
                const row = AppState.resultData[i];
                const longitude = row[AppState.longitudeColIndex];
                const latitude = row[AppState.latitudeColIndex];

                if (!Utils.isValidCoordinate(longitude, latitude)) {
                    row.push('已跳过 (无效经纬度)');
                    AppState.processStats.skipped++;
                    continue;
                }

                batchPromises.push(this.processRow(i, row, longitude, latitude, apiKey, detailData));
            }

            // 等待当前批次完成
            if (batchPromises.length > 0) {
                await Promise.allSettled(batchPromises);

                const progress = Math.round(((batchEnd - startRow) / totalRows) * 100);
                this.updateProgress(progress,
                    `已处理 ${batchEnd - startRow} 行，共 ${totalRows} 行 (成功: ${AppState.processStats.success}, 失败: ${AppState.processStats.failure}, 跳过: ${AppState.processStats.skipped})`);

                if (batchEnd < endRow) {
                    await new Promise(resolve => setTimeout(resolve, CONFIG.BATCH_DELAY));
                }
            }
        }
    },

    updateProgress(percent, status) {
        Elements.progressBar.style.width = percent + '%';
        Elements.progressText.textContent = percent + '%';
        Elements.processingStatus.textContent = status;
    }
};

// 继续DataProcessor模块
DataProcessor.processRow = async function(rowIndex, row, longitude, latitude, apiKey, detailData) {
    try {
        const geoInfo = await GeocodingAPI.getAddressFromLocation(longitude, latitude, apiKey);

        row.push('成功');
        AppState.processStats.success++;

        const detailRow = this.extractDetailedInfo(rowIndex, geoInfo, longitude, latitude);
        detailData.push(detailRow);

        return { success: true, rowIndex };
    } catch (error) {
        row.push(`失败 (${error.message})`);
        AppState.processStats.failure++;
        return { success: false, rowIndex, error };
    }
};

DataProcessor.extractDetailedInfo = function(rowIndex, geoInfo, longitude, latitude) {
    const addrComp = geoInfo.addressComponent || {};

    // 处理city字段
    let city = addrComp.city || '';
    if (Array.isArray(city) && city.length === 0) {
        city = '';
    }

    // 提取各种信息
    const neighborhood = addrComp.neighborhood || {};
    const building = addrComp.building || {};
    const streetNumber = addrComp.streetNumber || {};

    // 提取商圈信息
    let businessAreas = '';
    if (addrComp.businessAreas && addrComp.businessAreas.length > 0 && !Array.isArray(addrComp.businessAreas[0])) {
        businessAreas = addrComp.businessAreas.map(area => area.name || '').join(', ');
    }

    // 提取POI信息
    const poiInfo = this.extractPOIInfo(geoInfo.pois);

    return [
        rowIndex, // 原始行号
        poiInfo.id, poiInfo.name, poiInfo.type, poiInfo.tel, poiInfo.distance,
        longitude, latitude, geoInfo.formatted_address || '',
        addrComp.country || '', addrComp.province || '', city, addrComp.district || '',
        addrComp.citycode || '', addrComp.adcode || '', addrComp.township || '', addrComp.towncode || '',
        neighborhood.name || '', neighborhood.type || '',
        building.name || '', building.type || '',
        streetNumber.street || '', streetNumber.number || '',
        streetNumber.direction || '', streetNumber.distance || '',
        addrComp.seaArea || '', businessAreas
    ];
};

DataProcessor.extractPOIInfo = function(pois) {
    if (pois && pois.length > 0) {
        const nearestPoi = pois[0];
        return {
            id: nearestPoi.id || '',
            name: nearestPoi.name || '',
            type: nearestPoi.type || '',
            tel: nearestPoi.tel || '',
            distance: nearestPoi.distance || ''
        };
    }
    return { id: '', name: '', type: '', tel: '', distance: '' };
};

// 地理编码API模块
const GeocodingAPI = {
    async getAddressFromLocation(longitude, latitude, apiKey) {
        const maxRetries = 3;
        const initialBackoffDelay = 1000;

        return await this.fetchWithRetry(longitude, latitude, apiKey, 0, maxRetries, initialBackoffDelay);
    },

    async fetchWithRetry(longitude, latitude, apiKey, retryCount, maxRetries, initialBackoffDelay) {
        try {
            const url = `${CONFIG.GEO_URL}?key=${apiKey}&location=${longitude},${latitude}&radius=1000&extensions=all`;

            const response = await RequestQueue.enqueue(async () => {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), CONFIG.REQUEST_TIMEOUT);

                try {
                    const resp = await fetch(url, { signal: controller.signal });
                    clearTimeout(timeoutId);
                    return resp;
                } catch (error) {
                    clearTimeout(timeoutId);
                    throw error;
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP错误: ${response.status}`);
            }

            const data = await response.json();

            if (data.status !== '1') {
                throw new Error(`API错误: ${data.info || '未知错误'}`);
            }

            if (!data.regeocode) {
                throw new Error('API返回数据格式错误');
            }

            return data.regeocode;
        } catch (error) {
            const errorMsg = error.message || '未知错误';

            if (retryCount < maxRetries && this.isRetryableError(errorMsg)) {
                const backoffTime = this.calculateBackoff(retryCount, initialBackoffDelay);
                await new Promise(resolve => setTimeout(resolve, backoffTime));
                return this.fetchWithRetry(longitude, latitude, apiKey, retryCount + 1, maxRetries, initialBackoffDelay);
            }

            throw new Error(`获取地址信息失败: ${errorMsg}`);
        }
    },

    isRetryableError(errorMsg) {
        if (!errorMsg) return true;

        const retryableErrors = [
            'ECONNRESET', 'ETIMEDOUT', 'ECONNREFUSED', 'EPIPE', 'EHOSTUNREACH',
            'EAI_AGAIN', 'socket hang up', '429', 'too many requests', 'timeout',
            'network error', '请求超限'
        ];

        return retryableErrors.some(errText =>
            errorMsg.toLowerCase().includes(errText.toLowerCase())
        );
    },

    calculateBackoff(retryCount, initialBackoffDelay) {
        return Math.min(
            initialBackoffDelay * Math.pow(2, retryCount),
            10000 // 最大10秒
        );
    }
};

// 结果显示模块
const ResultDisplay = {
    showResults() {
        // 更新UI状态
        StepManager.goToStep3();

        // 更新结果摘要
        Elements.resultSummary.textContent =
            `共处理 ${AppState.resultData.length - 1} 条数据，成功: ${AppState.processStats.success}，失败: ${AppState.processStats.failure}，跳过: ${AppState.processStats.skipped}`;

        // 清理重复的状态列
        this.cleanupDuplicateStatusColumns();

        // 渲染结果表格
        this.renderResultTable();

        // 启用下载按钮
        Elements.downloadBtn.disabled = false;
    },

    cleanupDuplicateStatusColumns() {
        if (AppState.resultData.length === 0) return;

        const headers = AppState.resultData[0];
        const statusColumnIndices = [];

        headers.forEach((header, index) => {
            if (header === '处理状态') {
                statusColumnIndices.push(index);
            }
        });

        // 如果有多个处理状态列，只保留最后一个
        if (statusColumnIndices.length > 1) {
            // 从后向前删除，避免索引变化
            for (let i = statusColumnIndices.length - 2; i >= 0; i--) {
                const removeIndex = statusColumnIndices[i];

                // 从所有行中移除这一列
                for (let rowIndex = 0; rowIndex < AppState.resultData.length; rowIndex++) {
                    AppState.resultData[rowIndex].splice(removeIndex, 1);
                }
            }
        }
    },

    renderResultTable() {
        // 清空表格
        Utils.dom.clearElement(Elements.resultTableHeader);
        Utils.dom.clearElement(Elements.resultTableBody);

        if (AppState.resultData.length === 0) return;

        // 添加表头
        AppState.resultData[0].forEach(header => {
            const th = Utils.dom.createElement('th',
                'px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider',
                header);
            Elements.resultTableHeader.appendChild(th);
        });

        // 添加数据行（限制显示行数）
        const displayRows = Math.min(AppState.resultData.length, CONFIG.MAX_DISPLAY_ROWS + 1);

        for (let i = 1; i < displayRows; i++) {
            const row = AppState.resultData[i];
            const tr = document.createElement('tr');

            row.forEach((cell, cellIndex) => {
                const td = document.createElement('td');
                td.className = this.getCellClassName(cell, cellIndex, row.length);
                td.textContent = cell || '';
                tr.appendChild(td);
            });

            Elements.resultTableBody.appendChild(tr);
        }

        // 如果有更多行未显示，添加提示
        if (AppState.resultData.length > CONFIG.MAX_DISPLAY_ROWS + 1) {
            const tr = document.createElement('tr');
            const td = document.createElement('td');
            td.colSpan = AppState.resultData[0].length;
            td.className = 'px-3 py-4 text-center text-sm text-gray-500';
            td.textContent = `还有 ${AppState.resultData.length - CONFIG.MAX_DISPLAY_ROWS - 1} 行未显示，完整数据将包含在导出文件中`;
            tr.appendChild(td);
            Elements.resultTableBody.appendChild(tr);
        }
    },

    getCellClassName(cell, index, rowLength) {
        if (index === rowLength - 1) {
            // 处理状态列
            if (cell === '成功') {
                return 'px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600';
            } else if (cell && typeof cell === 'string' && cell.startsWith('失败')) {
                return 'px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600';
            } else if (cell && typeof cell === 'string' && cell.startsWith('已跳过')) {
                return 'px-6 py-4 whitespace-nowrap text-sm font-medium text-yellow-600';
            }
        } else if (index >= rowLength - 4 && index < rowLength - 1) {
            // 省市区列
            return 'px-6 py-4 whitespace-nowrap text-sm font-medium text-blue-600';
        }

        // 原始数据列
        return 'px-6 py-4 whitespace-nowrap text-sm text-gray-500';
    }
};

// 代码优化完成 - 所有功能已重构为模块化结构
// 版本: v1.0.0 - 优化完成