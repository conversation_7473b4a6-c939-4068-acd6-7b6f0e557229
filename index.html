<!--
经纬度逆向工具 - 主页面
版本: v1.0.0
变更记录:
- v1.0.0: 本地化CDN资源，优化页面加载速度，改进代码结构
- v0.5.0: 原始版本
-->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>【沈浪】经纬度逆向工具</title>
    <!-- 本地化TailwindCSS样式 -->
    <link rel="stylesheet" href="assets/css/tailwind-custom.css">
    <!-- 本地化xlsx.js库用于Excel处理 -->
    <script src="assets/js/xlsx.full.min.js"></script>
    <style>
        .loading {
            display: inline-block;
            width: 24px;
            height: 24px;
            border: 3px solid rgba(59, 130, 246, 0.3);
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s ease-in-out infinite;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        /* 自定义滚动条样式 */
        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }
        .custom-scrollbar::-webkit-scrollbar-track {
            background: #f1f1f1;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 3px;
        }
        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: #555;
        }
        /* 紧凑表格样式 */
        .compact-table th, .compact-table td {
            padding: 0.5rem 0.75rem;
            font-size: 0.875rem;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-2 py-4">
        <header class="mb-4 text-center">
            <h1 class="text-2xl font-bold text-gray-800">经纬度逆向工具 - 沈浪 v1.0</h1>
            <p class="text-gray-600 text-sm mt-1">上传Excel文件，自动根据经纬度为每个地址添加省/市/区县等详细点位信息</p>
        </header>

        <main class="max-w-5xl mx-auto bg-white rounded-lg shadow-md p-4">
            <!-- 步骤导航 -->
            <div class="flex justify-between mb-4">
                <div class="flex flex-col items-center">
                    <div id="step1-indicator" class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-bold text-sm">1</div>
                    <span class="mt-1 text-xs">上传文件</span>
                </div>
                <div class="flex-1 border-t-2 border-gray-300 self-center mx-2"></div>
                <div class="flex flex-col items-center">
                    <div id="step2-indicator" class="w-8 h-8 bg-gray-300 text-white rounded-full flex items-center justify-center font-bold text-sm">2</div>
                    <span class="mt-1 text-xs">数据处理</span>
                </div>
                <div class="flex-1 border-t-2 border-gray-300 self-center mx-2"></div>
                <div class="flex flex-col items-center">
                    <div id="step3-indicator" class="w-8 h-8 bg-gray-300 text-white rounded-full flex items-center justify-center font-bold text-sm">3</div>
                    <span class="mt-1 text-xs">下载结果</span>
                </div>
            </div>

            <!-- 步骤1：上传文件 -->
            <div id="step1" class="mb-4">
                <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                    <input type="file" id="fileInput" accept=".xlsx,.xls" class="hidden" />
                    <label for="fileInput" class="cursor-pointer">
                        <div class="flex flex-col items-center justify-center">
                            <svg class="w-12 h-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                            </svg>
                            <p class="mt-2 text-gray-600 text-sm">点击或拖拽Excel文件到此处</p>
                            <p class="text-xs text-gray-500 mt-1">支持 .xlsx 和 .xls 格式</p>
                        </div>
                    </label>
                </div>
                <div id="fileInfo" class="mt-3 hidden">
                    <div class="bg-blue-50 p-3 rounded-lg flex items-center">
                        <svg class="w-6 h-6 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <div>
                            <p id="fileName" class="font-medium text-blue-800 text-sm"></p>
                            <p id="fileSize" class="text-xs text-blue-600"></p>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex flex-col space-y-3">
                        <div class="flex flex-wrap items-center gap-3">
                            <div class="flex-grow max-w-xs">
                                <label class="block text-xs font-medium text-gray-700 mb-1">API Key</label>
                                <div class="relative">
                                    <input type="password" id="apiKey" value="" class="border border-gray-300 rounded-md px-3 py-1.5 w-full text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="输入高德地图API Key">
                                    <button type="button" id="toggleApiKey" class="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-600 hover:text-gray-800">
                                        <svg id="eyeIcon" class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                        <svg id="eyeOffIcon" class="h-4 w-4 hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.543 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            <div class="flex items-center mt-auto pb-1">
                                <input type="checkbox" id="saveApiKey" class="mr-1 h-3 w-3" checked>
                                <label for="saveApiKey" class="text-xs text-gray-600">保存API Key到本地</label>
                            </div>
                            <div class="ml-auto mt-auto">
                                <button id="nextBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-1.5 text-sm rounded-md font-medium disabled:bg-gray-300 disabled:cursor-not-allowed" disabled>下一步</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 步骤2：数据处理 -->
            <div id="step2" class="mb-4 hidden">
                <div class="bg-gray-50 p-3 rounded-lg mb-4">
                    <h3 class="font-medium text-gray-700 text-sm mb-2">数据预览</h3>
                    <div class="overflow-x-auto custom-scrollbar max-h-60">
                        <table id="previewTable" class="min-w-full divide-y divide-gray-200 compact-table">
                            <thead class="bg-gray-100">
                                <tr id="tableHeader"></tr>
                            </thead>
                            <tbody id="tableBody" class="bg-white divide-y divide-gray-200"></tbody>
                        </table>
                    </div>
                    <div class="mt-3 flex flex-wrap gap-3">
                        <div class="flex items-center">
                            <span class="text-xs text-gray-600 mr-1">经度列:</span>
                            <select id="longitudeCol" class="border border-gray-300 rounded-md px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"></select>
                        </div>
                        <div class="flex items-center">
                            <span class="text-xs text-gray-600 mr-1">纬度列:</span>
                            <select id="latitudeCol" class="border border-gray-300 rounded-md px-2 py-1 text-xs focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-transparent"></select>
                        </div>
                        <div class="flex items-center ml-auto">
                            <input type="checkbox" id="testMode" class="mr-1 h-3 w-3">
                            <label for="testMode" class="text-xs text-gray-600">测试模式（仅处理前5条）</label>
                        </div>
                    </div>
                </div>
                
                <div class="flex justify-between">
                    <button id="backBtn" class="border border-gray-300 text-gray-700 px-4 py-1.5 text-sm rounded-md font-medium hover:bg-gray-50">上一步</button>
                    <button id="processBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-1.5 text-sm rounded-md font-medium">开始处理</button>
                </div>
            </div>

            <!-- 步骤3：结果展示和下载 -->
            <div id="step3" class="mb-4 hidden">
                <div class="bg-green-50 p-4 rounded-lg text-center mb-4">
                    <svg class="w-10 h-10 text-green-500 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <h3 class="text-lg font-medium text-green-800 mt-2">处理完成！</h3>
                    <p class="text-green-600 text-sm mt-1" id="resultSummary"></p>
                </div>

                <div class="bg-gray-50 p-3 rounded-lg mb-4">
                    <h3 class="font-medium text-gray-700 text-sm mb-2">结果预览</h3>
                    <div class="overflow-x-auto custom-scrollbar max-h-60">
                        <table id="resultTable" class="min-w-full divide-y divide-gray-200 compact-table">
                            <thead class="bg-gray-100">
                                <tr id="resultTableHeader"></tr>
                            </thead>
                            <tbody id="resultTableBody" class="bg-white divide-y divide-gray-200"></tbody>
                        </table>
                    </div>
                </div>
                
                <div class="flex justify-between">
                    <button id="newFileBtn" class="border border-gray-300 text-gray-700 px-4 py-1.5 text-sm rounded-md font-medium hover:bg-gray-50">处理新文件</button>
                    <button id="downloadBtn" class="bg-green-500 hover:bg-green-600 text-white px-4 py-1.5 text-sm rounded-md font-medium">下载结果</button>
                </div>
            </div>

            <!-- 处理中状态 -->
            <div id="processing" class="hidden">
                <div class="flex flex-col items-center justify-center py-6">
                    <div class="loading mb-3"></div>
                    <h3 class="text-base font-medium text-gray-800 mb-1">正在处理数据...</h3>
                    <p class="text-gray-600 text-sm" id="processingStatus">准备中...</p>
                    <div class="w-full max-w-md bg-gray-200 rounded-full h-2 mt-3">
                        <div id="progressBar" class="bg-blue-600 h-2 rounded-full" style="width: 0%"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1" id="progressText">0%</p>
                </div>
            </div>
        </main>

        <footer class="mt-4 text-center text-gray-500 text-xs">
            <p>© 2025 经纬度逆向工具 | 沈浪制作</p>
        </footer>
    </div>

    <script src="app.js"></script>
</body>
</html> 