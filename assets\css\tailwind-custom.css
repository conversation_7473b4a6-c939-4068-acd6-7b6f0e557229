/**
 * 自定义TailwindCSS样式 - 经纬度逆向工具专用
 * 版本: v1.0.0
 * 变更记录:
 * - v1.0.0: 创建本地化TailwindCSS样式，包含项目所需的核心样式
 */

/* 重置样式 */
*, ::before, ::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: #e5e7eb;
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}

body {
  margin: 0;
  line-height: inherit;
}

/* 布局 */
.container {
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 0.5rem;
  padding-left: 0.5rem;
}

.mx-auto { margin-left: auto; margin-right: auto; }
.px-2 { padding-left: 0.5rem; padding-right: 0.5rem; }
.py-4 { padding-top: 1rem; padding-bottom: 1rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.py-1\.5 { padding-top: 0.375rem; padding-bottom: 0.375rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.pb-1 { padding-bottom: 0.25rem; }
.pr-3 { padding-right: 0.75rem; }
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }
.ml-auto { margin-left: auto; }

/* 显示 */
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }
.flex { display: flex; }

/* Flexbox */
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.flex-1 { flex: 1 1 0%; }
.flex-grow { flex-grow: 1; }

/* 尺寸 */
.w-full { width: 100%; }
.w-8 { width: 2rem; }
.w-6 { width: 1.5rem; }
.w-12 { width: 3rem; }
.w-10 { width: 2.5rem; }
.h-8 { height: 2rem; }
.h-6 { height: 1.5rem; }
.h-12 { height: 3rem; }
.h-10 { height: 2.5rem; }
.h-4 { height: 1rem; }
.h-3 { height: 0.75rem; }
.h-2 { height: 0.5rem; }
.max-w-5xl { max-width: 64rem; }
.max-w-xs { max-width: 20rem; }
.max-w-md { max-width: 28rem; }
.max-h-60 { max-height: 15rem; }
.min-h-screen { min-height: 100vh; }

/* 间距 */
.space-y-3 > :not([hidden]) ~ :not([hidden]) { margin-top: 0.75rem; }
.gap-3 > :not([hidden]) ~ :not([hidden]) { gap: 0.75rem; }

/* 文本 */
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.font-bold { font-weight: 700; }
.font-medium { font-weight: 500; }
.text-center { text-align: center; }
.text-left { text-align: left; }
.uppercase { text-transform: uppercase; }

/* 颜色 */
.text-gray-800 { color: #1f2937; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-500 { color: #6b7280; }
.text-white { color: #ffffff; }
.text-blue-800 { color: #1e40af; }
.text-blue-600 { color: #2563eb; }
.text-blue-500 { color: #3b82f6; }
.text-green-800 { color: #166534; }
.text-green-600 { color: #16a34a; }
.text-green-500 { color: #22c55e; }
.text-red-500 { color: #ef4444; }
.text-yellow-600 { color: #ca8a04; }

/* 背景色 */
.bg-gray-50 { background-color: #f9fafb; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-gray-200 { background-color: #e5e7eb; }
.bg-gray-300 { background-color: #d1d5db; }
.bg-white { background-color: #ffffff; }
.bg-blue-50 { background-color: #eff6ff; }
.bg-blue-500 { background-color: #3b82f6; }
.bg-blue-600 { background-color: #2563eb; }
.bg-green-50 { background-color: #f0fdf4; }
.bg-green-500 { background-color: #22c55e; }
.bg-green-600 { background-color: #16a34a; }

/* 边框 */
.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-t-2 { border-top-width: 2px; }
.border-dashed { border-style: dashed; }
.border-gray-300 { border-color: #d1d5db; }
.border-blue-500 { border-color: #3b82f6; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

/* 阴影 */
.shadow-md { box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1); }

/* 交互 */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }

/* 状态 */
.hover\:bg-blue-600:hover { background-color: #2563eb; }
.hover\:bg-green-600:hover { background-color: #16a34a; }
.hover\:bg-gray-50:hover { background-color: #f9fafb; }
.hover\:bg-gray-800:hover { background-color: #1f2937; }
.hover\:text-gray-800:hover { color: #1f2937; }

.focus\:outline-none:focus { outline: 2px solid transparent; outline-offset: 2px; }
.focus\:ring-2:focus { box-shadow: 0 0 0 3px rgb(59 130 246 / 0.5); }
.focus\:ring-1:focus { box-shadow: 0 0 0 1px rgb(59 130 246 / 0.5); }
.focus\:ring-blue-500:focus { --tw-ring-color: #3b82f6; }
.focus\:border-transparent:focus { border-color: transparent; }

.disabled\:bg-gray-300:disabled { background-color: #d1d5db; }
.disabled\:cursor-not-allowed:disabled { cursor: not-allowed; }

/* 表格 */
.min-w-full { min-width: 100%; }
.divide-y { border-top-width: 1px; }
.divide-gray-200 { border-color: #e5e7eb; }
.whitespace-nowrap { white-space: nowrap; }
.tracking-wider { letter-spacing: 0.05em; }

/* 溢出 */
.overflow-x-auto { overflow-x: auto; }

/* 定位 */
.relative { position: relative; }
.absolute { position: absolute; }
.inset-y-0 { top: 0; bottom: 0; }
.right-0 { right: 0; }

/* 其他 */
.self-center { align-self: center; }
.transition-opacity { transition-property: opacity; }
.duration-300 { transition-duration: 300ms; }
